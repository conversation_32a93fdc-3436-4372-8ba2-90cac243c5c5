<?php
// Test template processing

$template = file_get_contents('page template.htm');

$title = 'Test Title';
$content = '<h1>Test Header</h1><p>Test content</p>';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

echo "=== TEMPLATE CONTENT (first 500 chars) ===\n";
echo substr($template, 0, 500) . "\n\n";

echo "=== VARIABLES ===\n";
echo "Title: $title\n";
echo "Content: $content\n";
echo "CSS Path: $css_path\n\n";

// Start output buffering to capture PHP includes
ob_start();

// Set variables for template
extract([
    'title' => $title,
    'content' => $content,
    'css_path' => $css_path,
    'js_path' => $js_path,
    'base_url' => $base_url,
    'related_posts' => $related_posts
]);

// Evaluate the template
eval('?>' . $template);
$html = ob_get_clean();

echo "=== GENERATED HTML (first 1000 chars) ===\n";
echo substr($html, 0, 1000) . "\n";

// Look for the content section specifically
if (preg_match('/<div class="content">(.*?)<\/div>/s', $html, $matches)) {
    echo "\n=== CONTENT SECTION ===\n";
    echo $matches[1] . "\n";
} else {
    echo "\n=== NO CONTENT SECTION FOUND ===\n";
}
?>
