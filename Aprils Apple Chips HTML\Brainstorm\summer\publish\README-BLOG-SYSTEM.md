# A. A. Chips Simple Blog System

A simple, PHP-based blog system that converts Markdown files with YAML frontmatter into static HTML pages.

## Features

- **Markdown to HTML conversion** - Write posts in Markdown with YAML frontmatter
- **Component-based templates** - Reusable PHP includes for header, footer, sidebar
- **Modal system for shorthand content** - <PERSON><PERSON> posts display in modals instead of separate pages
- **JSON data generation** - Shorthand content compiled to JSON for easy modal loading
- **Simple build process** - One command generates all HTML files
- **Responsive design** - Uses your existing CSS framework

## File Structure

```
├── content/                 # Your Markdown files
│   ├── index.md            # Main index page content
│   ├── humor/              # Shorthand content (displays in modals)
│   └── *.md                # Regular blog posts
├── includes/               # PHP template components
│   ├── header.php          # Site header
│   ├── footer.php          # Site footer
│   └── sidebar.php         # Sidebar with navigation
├── generated/              # Generated HTML files
├── data/                   # Generated JSON data
│   └── humor.json          # Humor content for modals
├── css/                    # Your stylesheets
├── js/                     # JavaScript files
│   ├── script.js           # Existing functionality
│   └── modal.js            # Modal system
├── page template.htm       # Main template file
├── build.php              # Build script
└── build.bat              # Windows batch file to run build
```

## How to Use

### 1. Write Content

Create Markdown files in the `content/` directory with YAML frontmatter:

```markdown
---
title: My Blog Post
date: 2025-01-15
author: A. A. Chips
tags:
  - personal
  - journal
excerpt: A short description of the post
---

# My Blog Post

Your content here in **Markdown** format.

![Image](../img/image.jpg)

[Link to something](https://example.com)
```

### 2. Build the Site

Run the build process:

**Windows:**
```bash
build.bat
```

**Command Line:**
```bash
php build.php
```

### 3. View Results

- **Regular posts** → Generated as HTML files in `generated/` folder
- **Shorthand content** (humor) → Compiled to `data/humor.json` for modal display
- **Main index** → Generated as `index.html` in root directory

## Content Types

### Regular Posts
Any Markdown file will be converted to an HTML page. Place them anywhere in the `content/` directory.

### Shorthand Content (Modals)
Content in the `content/humor/` directory or with `humor` tag will be compiled to JSON and displayed in modals instead of separate pages.

## Template System

The system uses PHP includes for modularity:

- **`page template.htm`** - Main template structure
- **`includes/header.php`** - Site header and navigation
- **`includes/sidebar.php`** - Sidebar with categories and related posts
- **`includes/footer.php`** - Site footer

## Customization

### Adding New Categories
1. Create a new directory in `content/`
2. Add the category link to `includes/sidebar.php`
3. Rebuild the site

### Modifying Templates
Edit the PHP files in `includes/` to change the layout and structure.

### Styling
Modify `css/style.css` to change the appearance. Modal styles are included.

## Modal System

The modal system automatically handles shorthand content:

- Click "Humor" in the sidebar to see a list of humor posts
- Click any humor item to view it in a modal
- Easy navigation between items
- Responsive design

## Build Process

The build script:

1. Scans all `.md` files in `content/`
2. Parses YAML frontmatter and Markdown content
3. Converts Markdown to HTML
4. Generates individual HTML files for regular posts
5. Compiles shorthand content to JSON
6. Creates main index page

## Requirements

- PHP 7.0 or higher
- Web server (for viewing generated files)

## Tips

- Keep humor posts short for better modal experience
- Use descriptive filenames for better organization
- Images should be placed in the `img/` directory
- Test the build process after making changes

## Troubleshooting

- **Build fails**: Check PHP syntax in template files
- **Images not showing**: Verify image paths in Markdown
- **Modals not working**: Check that `modal.js` is loaded
- **Styling issues**: Verify CSS paths in generated HTML
