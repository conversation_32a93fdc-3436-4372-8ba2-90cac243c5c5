<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'Manhunt for <PERSON>';
$meta_description = 'The fugitive task force is looking for <PERSON> of Cincinnati for fraud. He has convinced 3 local churches that he is <PERSON> and even went as far as performing The Shape Of You in front of Christ Community Church last Sunday.';
$meta_keywords = '"#humor", A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'Manhunt for <PERSON>',
  'author' => 'Unknown',
  'tags' => 
  array (
    0 => '"#humor"',
  ),
  'excerpt' => 'The fugitive task force is looking for <PERSON> of Cincinnati for fraud. He has convinced 3 local churches that he is <PERSON> and even went as far as performing The Shape Of You in front of Christ Community Church last Sunday.',
);

// Raw content
$post_content = '<p>Scary stuff.</p>

<p>https://ifunny.co/picture/the-fugitive-task-force-is-looking-for-ronnie-williams-jr-ZTDYe3SX7</p>


<h3>Reposted by <a href="https://www.facebook.com/cityofcincinnati?__cft__[0]=AZUfyerO_s9nuMQlgioQ5Tn0q78zt0s66f_pIaQWCSQzvpA3nN6AmvvOUsAHmRCF9U5Ca6VF457erP6U3GUXa1TmLaAwj9cjcbIyC3oOwGwa1gW31hDPuzY6boNKlGcyrwIiSSmn9Gjqz6Wwl8iB7dyBAqv0vhcW08FACRPpBsMbJe0qiRq4egTodb4innn0LfHp8aTV-9gyDLDayVQRui05fhsH05bH_esdbQZKUWh4vw&__tn__=-UC%2CP-y-R"><strong>City of Cincinnati Government</strong></a></h3>

<img src="img/edsheeran.jpg" alt="edsheeran.jpg">

<p>The fugitive task force is looking for Ronnie Williams Jr. of Cincinnati for fraud. He has convinced 3 local churches that he is Ed Sheeran and even went as far as performing The Shape Of You in front of Christ Community Church last Sunday.</p>

<p>When the pastor was asked why he believed the real Ed Sheeran would perform for 35 dollars and a sandwich, he said he just assumed he had fell on tough times. If you have any information as to Ronnie’s whereabouts, message us immediately.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>