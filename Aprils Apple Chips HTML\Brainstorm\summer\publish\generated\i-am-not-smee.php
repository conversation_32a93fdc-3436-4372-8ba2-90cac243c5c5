<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'I am not Smee';
$meta_description = 'I\'ve got to address something. I was told today I was giving off heavy Mr. Smee vibes, as in the supporting antagonist from <PERSON>. I want to put on record, that I look nothing like Mr. <PERSON>mee. I had a picture taken to prove how little of a resemblance there is. I am not <PERSON>mee.';
$meta_keywords = '';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'I am not Smee',
  'author' => 'A. A. Chips',
  'excerpt' => 'I\'ve got to address something. I was told today I was giving off heavy Mr. Smee vibes, as in the supporting antagonist from <PERSON>. I want to put on record, that I look nothing like Mr. Smee. I had a picture taken to prove how little of a resemblance there is. I am not Smee.',
  '"Date' => '": 5/15/2025',
  'categories' => 
  array (
    0 => 'Journal',
    1 => 'This is me',
  ),
);

// Raw content
$post_content = '<p>I\'ve got to address something. I was told today I was giving off heavy Mr. Smee vibes, as in the supporting antagonist from Hook. I want to put on record, that I look nothing like Mr. Smee. I had a picture taken to prove how little of a resemblance there is. I am not Smee.</p>

<img src="img/Pasted image 20250515100439.png" alt="Pasted image 20250515100439.png">';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>