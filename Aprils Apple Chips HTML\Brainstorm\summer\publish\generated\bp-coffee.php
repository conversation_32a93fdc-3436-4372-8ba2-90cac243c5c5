<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = '"BP Spills Coffee: A Parody by UCB Comedy"';
$meta_description = '"\'This is what happens when BP spills coffee.\'"';
$meta_keywords = '"#humor", A. A<PERSON> Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => '"BP Spills Coffee: A Parody by UCB Comedy"',
  '"Date' => '": June 9, 2010',
  'tags' => 
  array (
    0 => '"#humor"',
  ),
  'excerpt' => '"\'This is what happens when BP spills coffee.\'"',
);

// Raw content
$post_content = '<iframe width="560" height="315" src="https://www.youtube.com/embed/2AAa0gd7ClM?si=k5uYJT6s01yw7jSW" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<h1>BP Spills Coffee: a PARODY by UCB Comedy</h1>

<p><a href="https://yt3.ggpht.com/4zXWXpQMf4UeL4pNp-8JLNNm-jqHlS1Sa1O_ZISHAZ_QuaLrG6GdXEpTxlEwMP8RDWbrU6mm=s88-c-k-c0x00ffffff-no-rj"><img src="https://www.youtube.com/@UCBComedy" alt="</a>"></p>

<p><a href="https://www.youtube.com/@UCBComedy">Upright Citizens Brigade</a></p>
<p>127K subscribers</p>
<p>13,714,682 views Jun 9, 2010</p>

<p>This is what happens when BP spills coffee. SUBSCRIBE:    <a href="https://www.gstatic.com/youtube/img/watch/yt_favicon_ringo2.png"><img src="http://www.youtube.com/user/UCBComedy" alt="</a> / ucbcomedy">   If you liked this video, check out more videos from UCB Comedy:    <a href="https://www.gstatic.com/youtube/img/watch/yt_favicon_ringo2.png"><img src="https://www.youtube.com/watch?v=u3sK2O8I0hI&list=PLvMZxK22whIsWSSFONDd53FGZVBv-QtfU&index=1" alt="</a> • Edward Snowden\'s Girlfriend is Hot: a...">   Director: Peter Schultz & Brandon Bassham Writers: Gavin Speiller, Eric Scott, Erik Tanouye, & John Frusciante Editor: Peter Schultz Starring: Eric Scott, Nat Freedberg, Kevin Cragg, Gavin Speiller, Kate McKinnon, John Frusciante, Zhubin Parang, Devlyn Corrigan, Erik Tanouye, Rob Lathan Producer: Todd Bieber Like UCB:   <a href="https://www.gstatic.com/youtube/img/watch/social_media/facebook_1x.png"><img src="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbG9kZlBISXVrSjQ2bGZkMlJsSk50UEVWNVI1Z3xBQ3Jtc0tsdXQwQnV1SWswVTVlam1fRktaS0NaMG8zajRiNlcwOUhvbndvel9FMFEzWDhtNWxzSGl4VndwdTBHM3NMOWxVY242M1JYZDRXRmhpc0V3Vm12WEtFMDJWYWhOUDllak92WGFIT0FHOUdsaGt3TlJoNA&q=http%3A%2F%2Fwww.facebook.com%2FUCBcomedy&v=2AAa0gd7ClM" alt="</a> / ucbcomedy">   Follow UCB:   <a href="https://www.gstatic.com/youtube/img/watch/social_media/twitter_1x_v2.png"><img src="https://www.youtube.com/redirect?event=video_description&redir_token=QUFFLUhqbmhzWk1Geld6bDd4dExtZi0waG1hbjRZazZ5QXxBQ3Jtc0tsdkVjdTRDMjh2aEsxMnBjYXA0Tkg1elpnOWpNaUZKMGI1d05XZmpNZnVBdktkTnN5eGVRQkc1S1F5cHZWUm9aUGFRWE1reC15LTRGWEtRSGxlRWdYeVRVX2FzTUxYOEIxUXBRNktCX0E5TkhSdVRtcw&q=http%3A%2F%2Fwww.twitter.com%2FUCBcomedy&v=2AAa0gd7ClM" alt="</a> / ucbcomedy"></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>