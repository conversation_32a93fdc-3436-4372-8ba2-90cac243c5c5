// Modal functionality for shorthand content
class ContentModal {
    constructor() {
        this.modal = document.getElementById('contentModal');
        this.modalBody = document.getElementById('modalBody');
        this.closeBtn = document.querySelector('.close');
        this.humorData = null;
        
        this.init();
        this.loadHumorData();
    }
    
    init() {
        // Close modal when clicking the X
        if (this.closeBtn) {
            this.closeBtn.onclick = () => this.close();
        }
        
        // Close modal when clicking outside of it
        window.onclick = (event) => {
            if (event.target === this.modal) {
                this.close();
            }
        };
        
        // Close modal with Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.modal.style.display === 'block') {
                this.close();
            }
        });
    }
    
    async loadHumorData() {
        try {
            const response = await fetch('data/humor.json');
            this.humorData = await response.json();
        } catch (error) {
            console.error('Error loading humor data:', error);
            this.humorData = [];
        }
    }
    
    open(content) {
        this.modalBody.innerHTML = content;
        this.modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }
    
    close() {
        this.modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // Restore scrolling
    }
    
    showHumorList() {
        if (!this.humorData || this.humorData.length === 0) {
            this.open('<h2>Humor</h2><p>No humor content available at the moment.</p>');
            return;
        }
        
        let content = '<h2>Humor Vault</h2><div class="humor-list">';
        
        this.humorData.forEach((item, index) => {
            content += `
                <div class="humor-item" onclick="contentModal.showHumorItem(${index})">
                    <h3>${item.title}</h3>
                    <p class="humor-excerpt">${item.excerpt || 'Click to read more...'}</p>
                    <div class="humor-meta">
                        ${item.author ? `<span>by ${item.author}</span>` : ''}
                        ${item.date ? `<span>${item.date}</span>` : ''}
                    </div>
                </div>
            `;
        });
        
        content += '</div>';
        this.open(content);
    }
    
    showHumorItem(index) {
        if (!this.humorData || !this.humorData[index]) {
            this.open('<h2>Error</h2><p>Content not found.</p>');
            return;
        }
        
        const item = this.humorData[index];
        let content = `
            <div class="humor-content">
                <h2>${item.title}</h2>
                ${item.author ? `<p class="author">by ${item.author}</p>` : ''}
                ${item.date ? `<p class="date">${item.date}</p>` : ''}
                <div class="content-body">
                    ${item.content}
                </div>
                <button onclick="contentModal.showHumorList()" class="back-btn">← Back to Humor List</button>
            </div>
        `;
        
        this.open(content);
    }
}

// Initialize modal when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.contentModal = new ContentModal();
});

// Global function for humor modal (called from sidebar)
function showHumorModal() {
    if (window.contentModal) {
        window.contentModal.showHumorList();
    }
}
