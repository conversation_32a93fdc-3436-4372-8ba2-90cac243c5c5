{"name": "markdown-blog", "version": "1.0.0", "description": "A blog system that parses Markdown files into HTML", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["markdown", "blog", "obsidian"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "marked": "^9.1.0", "highlight.js": "^11.8.0", "front-matter": "^4.0.2", "chokidar": "^3.5.3", "ejs": "^3.1.9", "fs-extra": "^11.1.1", "slugify": "^1.6.6", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.1"}}