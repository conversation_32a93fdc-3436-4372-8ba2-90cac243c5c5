const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const marked = require('marked');
const frontMatter = require('front-matter');
const chokidar = require('chokidar');
const slugify = require('slugify');
const moment = require('moment');
const hljs = require('highlight.js');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Set up EJS as the view engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'templates'));

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));
app.use('/static', express.static(path.join(__dirname, 'static')));

// Configure marked with highlight.js for code syntax highlighting
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      return hljs.highlight(lang, code).value;
    }
    return hljs.highlightAuto(code).value;
  },
  headerIds: true,
  gfm: true
});

// Paths
const contentDir = path.join(__dirname, 'content');
const publicDir = path.join(__dirname, 'public');
const postsDir = path.join(publicDir, 'posts');

// Ensure directories exist
fs.ensureDirSync(contentDir);
fs.ensureDirSync(publicDir);
fs.ensureDirSync(postsDir);

// Store posts in memory for quick access
let posts = [];
let tags = {};

// Function to parse markdown files
function parseMarkdownFile(filePath) {
  const fileName = path.basename(filePath);
  const fileContent = fs.readFileSync(filePath, 'utf8');
  
  // Parse front matter
  const { attributes, body } = frontMatter(fileContent);
  
  // Generate slug from title or filename
  const slug = slugify(attributes.title || path.basename(fileName, '.md'), { 
    lower: true,
    strict: true
  });
  
  // Convert markdown to HTML
  let html = marked.parse(body);
  
  // Process hashtags
  const hashtags = [];
  const hashtagRegex = /#([a-zA-Z0-9_]+)/g;
  let match;
  
  while ((match = hashtagRegex.exec(body)) !== null) {
    hashtags.push(match[1]);
  }
  
  // Process internal links
  html = html.replace(/\\[\\[([^\\]]+)\\]\\]/g, (match, p1) => {
    const linkText = p1.split('|')[1] || p1;
    const linkTarget = p1.split('|')[0];
    const linkSlug = slugify(linkTarget, { lower: true, strict: true });
    return `<a href="/posts/${linkSlug}" class="internal-link">${linkText}</a>`;
  });
  
  // Create post object
  const post = {
    title: attributes.title || fileName.replace('.md', ''),
    date: attributes.date ? moment(attributes.date).format('MMMM D, YYYY') : moment().format('MMMM D, YYYY'),
    slug,
    tags: attributes.tags || hashtags,
    excerpt: attributes.excerpt || body.substring(0, 200) + '...',
    content: html,
    filePath
  };
  
  return post;
}

// Function to process all markdown files
function processMarkdownFiles() {
  posts = [];
  tags = {};
  
  const files = fs.readdirSync(contentDir).filter(file => file.endsWith('.md'));
  
  files.forEach(file => {
    const filePath = path.join(contentDir, file);
    const post = parseMarkdownFile(filePath);
    posts.push(post);
    
    // Add to tags
    if (post.tags) {
      post.tags.forEach(tag => {
        if (!tags[tag]) {
          tags[tag] = [];
        }
        tags[tag].push(post);
      });
    }
  });
  
  // Sort posts by date (newest first)
  posts.sort((a, b) => new Date(b.date) - new Date(a.date));
  
  console.log(`Processed ${posts.length} posts with ${Object.keys(tags).length} tags`);
}

// Watch for file changes
const watcher = chokidar.watch(contentDir, {
  ignored: /(^|[\/\\])\../,
  persistent: true
});

watcher
  .on('add', path => {
    console.log(`File ${path} has been added`);
    processMarkdownFiles();
  })
  .on('change', path => {
    console.log(`File ${path} has been changed`);
    processMarkdownFiles();
  })
  .on('unlink', path => {
    console.log(`File ${path} has been removed`);
    processMarkdownFiles();
  });

// Routes
app.get('/', (req, res) => {
  res.render('index', { 
    posts,
    tags: Object.keys(tags),
    title: 'My Markdown Blog'
  });
});

app.get('/posts/:slug', (req, res) => {
  const post = posts.find(p => p.slug === req.params.slug);
  
  if (!post) {
    return res.status(404).render('404', { title: 'Post Not Found' });
  }
  
  res.render('post', { 
    post,
    title: post.title,
    tags: Object.keys(tags)
  });
});

app.get('/tag/:tag', (req, res) => {
  const tag = req.params.tag;
  const tagPosts = tags[tag] || [];
  
  res.render('tag', { 
    posts: tagPosts,
    tag,
    tags: Object.keys(tags),
    title: `Posts tagged with #${tag}`
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
  processMarkdownFiles();
});
