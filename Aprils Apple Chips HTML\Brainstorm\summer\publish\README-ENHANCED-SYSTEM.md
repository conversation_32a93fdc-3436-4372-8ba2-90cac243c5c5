# Enhanced Blog System

This is an improved version of the blog system with templating, configuration management, and dynamic PHP generation.

## 🚀 Key Improvements

### ✅ Phase 1: Configuration & Variables
- **Central configuration** in `config.php` for site-wide settings
- **Dynamic head metadata** with proper SEO tags
- **Enhanced post headers** with better formatting and metadata display
- **Configurable navigation** and categories

### ✅ Phase 2: True Include System  
- **Dynamic PHP files** instead of static HTML
- **Runtime includes** for header, sidebar, and footer
- **Template variables** passed to includes
- **Reduced code duplication**

### ✅ Phase 3: Enhanced Post Sections
- **Improved metadata display** with icons and proper formatting
- **Dynamic tag links** with slug generation
- **Better date formatting**
- **Structured post content** with proper CSS classes

### ✅ Phase 4: User-Friendly Content Management
- **Helper scripts** for creating new posts
- **Content validation** with error checking
- **Improved build process** with better error handling

## 📁 File Structure

```
├── config.php              # Site configuration
├── build.php               # Enhanced build system
├── new-post.php            # Helper for creating new posts
├── validate-content.php    # Content validation script
├── page template.htm       # Main template file
├── includes/               # Template includes
│   ├── header.php          # Dynamic header
│   ├── sidebar.php         # Dynamic sidebar  
│   └── footer.php          # Footer
├── content/                # Markdown source files
├── generated/              # Generated PHP files
├── css/                    # Stylesheets
└── js/                     # JavaScript files
```

## 🛠️ Usage

### Building the Site
```bash
php build.php
```

### Creating New Posts
```bash
php new-post.php "My New Post Title" journal
php new-post.php "Another Post" advocacy
```

### Validating Content
```bash
php validate-content.php
```

## ⚙️ Configuration

Edit `config.php` to customize:

- **Site information** (name, title, description)
- **SEO settings** (keywords, meta tags)
- **Navigation menu** items
- **Categories** and their URLs
- **Build settings** (PHP vs HTML generation)
- **External services** (Ko-Fi, analytics)

## 📝 Content Format

### YAML Frontmatter
```yaml
---
title: "Your Post Title"
date: 2025-01-15
author: "A. A. Chips"
tags:
  - journal
  - personal
excerpt: "Brief description for SEO and previews"
---
```

### Required Fields
- `title`: Post title
- `date`: Publication date (YYYY-MM-DD format)
- `author`: Author name

### Optional Fields
- `tags`: Array of tags for categorization
- `excerpt`: Brief description for SEO
- `description`: Alternative to excerpt
- `reading_time`: Estimated reading time in minutes

## 🎨 Template System

### Variables Available in Templates
- `$config`: Full site configuration
- `$page_title`: Current page title
- `$meta_description`: Page description
- `$meta_keywords`: Page keywords
- `$css_path`: Path to CSS files
- `$js_path`: Path to JavaScript files
- `$base_url`: Base URL for links
- `$content`: Page content
- `$related_posts`: Related posts array

### Dynamic Content Generation
PHP files now generate content dynamically:
- Post metadata is processed at runtime
- Tags create proper links
- Dates are formatted consistently
- Content is properly escaped for security

## 🔧 Customization

### Adding New Categories
1. Edit `config.php` and add to the `categories` array
2. Create corresponding directory in `content/`
3. Rebuild with `php build.php`

### Modifying Templates
- Edit `page template.htm` for overall page structure
- Modify files in `includes/` for specific sections
- Use configuration variables for dynamic content

### Styling
- Edit `css/style.css` for visual customization
- New CSS classes available:
  - `.post-header`, `.post-title`, `.post-meta`
  - `.post-excerpt`, `.post-tags`, `.tag`
  - `.meta-separator`, `.tags-label`

## 🚀 Benefits

1. **Maintainability**: Changes to header/footer update all pages
2. **Consistency**: Centralized configuration ensures uniformity
3. **SEO**: Proper meta tags and structured data
4. **User-Friendly**: Helper scripts make content creation easy
5. **Validation**: Built-in checks prevent common errors
6. **Performance**: PHP includes reduce file sizes
7. **Flexibility**: Easy to customize and extend

## 🔄 Migration from Old System

The system is backward compatible. Existing content will work, but you'll get the benefits of:
- Better formatted metadata
- Dynamic includes
- Centralized configuration
- Improved SEO

## 📋 Next Steps

Consider adding:
- Related posts functionality
- Search capability
- RSS feed generation
- Image optimization
- Caching system
- Admin interface
