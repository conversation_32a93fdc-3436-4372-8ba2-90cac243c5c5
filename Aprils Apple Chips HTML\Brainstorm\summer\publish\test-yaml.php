<?php
// Test YAML parsing for Hadestown review
$file = 'content/hadestown-review.md';
$content = file_get_contents($file);

echo "File content:\n";
echo substr($content, 0, 500) . "\n\n";

// Test the parsing
if (preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
    echo "YAML section found:\n";
    echo $matches[1] . "\n\n";
    
    $frontmatter = [];
    $yamlLines = explode("\n", $matches[1]);
    $currentKey = null;
    
    foreach ($yamlLines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        echo "Processing line: '$line'\n";
        
        // Handle array items
        if (strpos($line, '-') === 0) {
            if ($currentKey && !isset($frontmatter[$currentKey])) {
                $frontmatter[$currentKey] = [];
            }
            if ($currentKey) {
                $frontmatter[$currentKey][] = trim(substr($line, 1));
            }
        } elseif (strpos($line, ':') !== false) {
            [$key, $value] = explode(':', $line, 2);
            $key = trim($key);
            $value = trim($value);
            $currentKey = $key;
            
            if (!empty($value)) {
                $frontmatter[$key] = $value;
            }
        }
    }
    
    echo "\nParsed frontmatter:\n";
    print_r($frontmatter);
} else {
    echo "No YAML frontmatter found!\n";
}
?>
