<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'How to Support April\'s Apple Chips';
$meta_description = 'Help a chip off the old block get their business off the ground.. Every cold season for the past six years I have made apple chips. Most of these are given out for free, many of them are sold. In the off-season, I work on the administration and side projects.';
$meta_keywords = 'aachips, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'How to Support April\'s Apple Chips',
  'tags' => 
  array (
    0 => 'aachips',
  ),
  'author' => 'A. A. Chips',
  'excerpt' => 'Help a chip off the old block get their business off the ground.. Every cold season for the past six years I have made apple chips. Most of these are given out for free, many of them are sold. In the off-season, I work on the administration and side projects.',
  'date' => '2025-05-01',
);

// Raw content
$post_content = '<p>Help a chip off the old block get their business off the ground..</p>

<p>Every cold season for the past six years I have made apple chips. Most of these are given out for free, many of them are sold. In the off-season, I work on the administration and side projects. Sometimes I pick up private cooking jobs. My part time job, which eats up all of my energy, is teaching, is 18 hours a week. I have bills, aspirations, and people and places I wish to support. Here are some ways you can be the wind beneath my wings..</p>

<ul><li>I have a [[Wishlist]]. </li>
<p><li>Buy bags of apple chips as pay-it-forward, to cover the expense of gifting free bags of apple chips to </li></p>
<p><li>Donate directly to my <a href="https://www.paypal.me/bbushwick" target="_blank">Paypal</a>, or my <a href="https://www.ko-fi.com/aachips" target="_blank">Ko-Fi</a> campaign. Ko-Fi allows monthly pledges. Maintaining a one or two dollar a month pledge is more valuable than twenty dollars at one time. </li></p>
<p><li><a href="https://www.aachips.co/order" target="_blank">Pre-order bags of apple chips</a> before the season starts. First come, first serve.</li></p>
<p><li>Check out my <a href="https://www.aachips.co/store" target="_blank">webstore (coming soon)</a>. </li></p>
<p><li>Hire me to come and cook for you, your family, and/or your event. You provide the kitchen.</li></p>
<p><li>I also do cooking workshops and coaching to help people become better home cooks.</li></p>
<p><li>I can help you set up, maintain, or fix your website. I work in code, as well as WordPress.</li></p>
<p><li>Write a testimonial if I\'ve cooked with, or for you in the past, or if you have had my apple chips. </li></p>
<p><li>For testimonials about the apple chips, check out my guide on [[bad-chip-testimonial-guide]]. You can <a href="https://www.aachips.co/testimonials.php" target="_blank">drop these testimonials here</a>.</li></p>
<p><li>Help me user test projects such as <a href="https://www.aachips.co/heartwarmers" target="_blank">Heartwarmers</a>. Submit bugs to me or features you think are good through the chat widget. </li></ul></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>