<?php
/**
 * Simple Blog Builder
 * Converts Markdown files with YAML frontmatter to HTML pages
 */

class BlogBuilder {
    private $contentDir = 'content';
    private $outputDir = 'generated';
    private $dataDir = 'data';
    private $templateFile = 'page template.htm';

    public function __construct() {
        // Create output directories if they don't exist
        if (!is_dir($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
    }

    public function build() {
        echo "Starting blog build...\n";

        // Process all markdown files
        $this->processMarkdownFiles();

        // Generate JSON data for shorthand content
        $this->generateHumorData();

        // Generate main index page
        $this->generateIndexPage();

        echo "Build complete!\n";
    }

    private function processMarkdownFiles() {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->contentDir)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'md') {
                $this->processMarkdownFile($file);
            }
        }
    }

    private function processMarkdownFile($file) {
        $content = file_get_contents($file->getPathname());
        $parsed = $this->parseMarkdown($content);

        // Skip if no frontmatter
        if (!$parsed['frontmatter']) {
            return;
        }

        $frontmatter = $parsed['frontmatter'];
        $markdownContent = $parsed['content'];

        // Convert markdown to HTML
        $htmlContent = $this->markdownToHtml($markdownContent);

        // Determine if this should be shorthand content (humor section)
        $isShorthand = $this->isShorthandContent($file, $frontmatter);

        if ($isShorthand) {
            // Store for JSON generation instead of creating HTML file
            return;
        }

        // Generate HTML file
        $this->generateHtmlFile($file, $frontmatter, $htmlContent);
    }

    private function parseMarkdown($content) {
        // Simple YAML frontmatter parser
        if (!preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
            return ['frontmatter' => null, 'content' => $content];
        }

        $frontmatter = [];
        $yamlLines = explode("\n", $matches[1]);
        $currentKey = null;

        foreach ($yamlLines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Handle array items
            if (strpos($line, '-') === 0) {
                if ($currentKey && !isset($frontmatter[$currentKey])) {
                    $frontmatter[$currentKey] = [];
                }
                if ($currentKey) {
                    $frontmatter[$currentKey][] = trim(substr($line, 1));
                }
            } elseif (strpos($line, ':') !== false) {
                [$key, $value] = explode(':', $line, 2);
                $key = trim($key);
                $value = trim($value);
                $currentKey = $key;

                if (!empty($value)) {
                    $frontmatter[$key] = $value;
                }
            }
        }

        return ['frontmatter' => $frontmatter, 'content' => $matches[2]];
    }

    private function markdownToHtml($markdown) {
        // Simple markdown to HTML conversion
        $html = $markdown;

        // Headers (but don't convert if it's already HTML)
        $html = preg_replace('/^### (.*$)/m', '<h3>$1</h3>', $html);
        $html = preg_replace('/^## (.*$)/m', '<h2>$1</h2>', $html);
        // Don't convert # headers if they're already in HTML format
        $html = preg_replace('/^# (?!.*<h1>)(.*$)/m', '<h1>$1</h1>', $html);

        // Bold and italic
        $html = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $html);
        $html = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $html);

        // Links
        $html = preg_replace('/\[([^\]]+)\]\(([^)]+)\)/', '<a href="$2">$1</a>', $html);

        // Images
        $html = preg_replace('/!\[\[([^\]]+)\]\]/', '<img src="img/$1" alt="$1">', $html);
        $html = preg_replace('/!\[([^\]]*)\]\(([^)]+)\)/', '<img src="$2" alt="$1">', $html);

        // Lists
        $html = preg_replace('/^\+ (.*)$/m', '<li>$1</li>', $html);
        $html = preg_replace('/^- (.*)$/m', '<li>$1</li>', $html);

        // Wrap consecutive list items in ul tags
        $html = preg_replace('/(<li>.*<\/li>)/s', '<ul>$1</ul>', $html);

        // Paragraphs
        $lines = explode("\n", $html);
        $result = [];

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                $result[] = '';
                continue;
            }

            // Don't wrap HTML tags in paragraphs
            if (strpos($line, '<h') === 0 || strpos($line, '<ul>') === 0 ||
                strpos($line, '<img') === 0 || strpos($line, '<iframe') === 0 ||
                strpos($line, '<div') === 0 || strpos($line, '<hr') === 0 ||
                strpos($line, '</div>') === 0) {
                $result[] = $line;
            } else {
                $result[] = "<p>{$line}</p>";
            }
        }

        return implode("\n", $result);
    }

    private function isShorthandContent($file, $frontmatter) {
        // Check if file is in humor directory or has humor tag
        $path = $file->getPathname();
        if (strpos($path, 'humor') !== false) {
            return true;
        }

        if (isset($frontmatter['tags'])) {
            $tags = $frontmatter['tags'];
            // Handle both array and string tags
            if (is_array($tags) && in_array('humor', $tags)) {
                return true;
            } elseif (is_string($tags) && $tags === 'humor') {
                return true;
            }
        }

        return false;
    }

    private function generateHtmlFile($file, $frontmatter, $content) {
        // Load template
        $template = file_get_contents($this->templateFile);

        // Prepare variables for template
        $title = $frontmatter['title'] ?? 'Untitled';
        $css_path = '../css/';
        $js_path = '../js/';
        $base_url = '../';

        // Generate post header with metadata
        $postHeader = $this->generatePostHeader($frontmatter);
        $fullContent = $postHeader . $content;

        // Start output buffering to capture PHP includes
        ob_start();

        // Set variables for template
        extract([
            'title' => $title,
            'content' => $fullContent,
            'css_path' => $css_path,
            'js_path' => $js_path,
            'base_url' => $base_url,
            'related_posts' => []
        ]);

        // Evaluate the template
        eval('?>' . $template);
        $html = ob_get_clean();

        // Generate output filename
        $relativePath = str_replace($this->contentDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
        $outputPath = $this->outputDir . DIRECTORY_SEPARATOR . str_replace('.md', '.html', $relativePath);

        // Create directory if needed
        $outputDir = dirname($outputPath);
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }

        // Write HTML file
        file_put_contents($outputPath, $html);
        echo "Generated: $outputPath\n";
    }

    private function generatePostHeader($frontmatter) {
        $header = '';

        // Debug: Let's see what frontmatter we have
        if (empty($frontmatter)) {
            return $header;
        }

        // Add title
        if (isset($frontmatter['title'])) {
            $header .= "<h1>{$frontmatter['title']}</h1>\n";
        }

        // Add post metadata
        $metadata = [];
        if (isset($frontmatter['author'])) {
            $metadata[] = "<span class=\"post-author\">By {$frontmatter['author']}</span>";
        }
        if (isset($frontmatter['date'])) {
            $metadata[] = "<span class=\"post-date\">{$frontmatter['date']}</span>";
        }

        if (!empty($metadata)) {
            $header .= "<div class=\"post-meta\">" . implode(' • ', $metadata) . "</div>\n";
        }

        // Add excerpt if available
        if (isset($frontmatter['excerpt'])) {
            $header .= "<div class=\"post-excerpt\"><em>{$frontmatter['excerpt']}</em></div>\n";
        }

        // Add tags if available
        if (isset($frontmatter['tags'])) {
            $tags = $frontmatter['tags'];
            if (is_array($tags) && !empty($tags)) {
                $header .= "<div class=\"post-tags\">";
                foreach ($tags as $tag) {
                    $header .= "<span class=\"tag\">{$tag}</span>";
                }
                $header .= "</div>\n";
            } elseif (is_string($tags)) {
                $header .= "<div class=\"post-tags\"><span class=\"tag\">{$tags}</span></div>\n";
            }
        }

        // Add separator
        $header .= "<hr class=\"post-separator\">\n\n";

        return $header;
    }

    private function generateHumorData() {
        $humorData = [];
        $humorDir = $this->contentDir . '/humor';

        if (!is_dir($humorDir)) {
            file_put_contents($this->dataDir . '/humor.json', json_encode([]));
            return;
        }

        $files = glob($humorDir . '/*.md');

        foreach ($files as $file) {
            $content = file_get_contents($file);
            $parsed = $this->parseMarkdown($content);

            if ($parsed['frontmatter']) {
                $frontmatter = $parsed['frontmatter'];
                $htmlContent = $this->markdownToHtml($parsed['content']);

                $humorData[] = [
                    'title' => $frontmatter['title'] ?? basename($file, '.md'),
                    'author' => $frontmatter['author'] ?? null,
                    'date' => $frontmatter['date'] ?? null,
                    'excerpt' => $this->generateExcerpt($parsed['content']),
                    'content' => $htmlContent,
                    'tags' => $frontmatter['tags'] ?? []
                ];
            }
        }

        file_put_contents($this->dataDir . '/humor.json', json_encode($humorData, JSON_PRETTY_PRINT));
        echo "Generated humor data: " . count($humorData) . " items\n";
    }

    private function generateIndexPage() {
        // Check if index.md exists in content directory
        $indexFile = $this->contentDir . '/index.md';

        if (file_exists($indexFile)) {
            // Process the existing index.md file
            $content = file_get_contents($indexFile);
            $parsed = $this->parseMarkdown($content);

            if ($parsed['frontmatter']) {
                $frontmatter = $parsed['frontmatter'];
                $htmlContent = $this->markdownToHtml($parsed['content']);

                // Generate index.html in root directory
                $this->generateRootIndexFile($frontmatter, $htmlContent);
            }
        } else {
            // Generate a default index page
            $this->generateDefaultIndexPage();
        }
    }

    private function generateRootIndexFile($frontmatter, $content) {
        // Load template
        $template = file_get_contents($this->templateFile);

        // Prepare variables for template
        $title = $frontmatter['title'] ?? 'Welcome to A. A. Chips\' Blog';
        $css_path = 'css/';
        $js_path = 'js/';
        $base_url = '';

        // Start output buffering to capture PHP includes
        ob_start();

        // Set variables for template
        extract([
            'title' => $title,
            'content' => $content,
            'css_path' => $css_path,
            'js_path' => $js_path,
            'base_url' => $base_url,
            'related_posts' => []
        ]);

        // Evaluate the template
        eval('?>' . $template);
        $html = ob_get_clean();

        // Write index.html file in root
        file_put_contents('index.html', $html);
        echo "Generated: index.html\n";
    }

    private function generateDefaultIndexPage() {
        $defaultContent = '<h1>Welcome to A. A. Chips\' Digital Garden</h1>
        <p>This is a simple blog system built from Markdown files.</p>
        <p>Check out the generated content in the <a href="generated/">generated folder</a>.</p>';

        $this->generateRootIndexFile(['title' => 'A. A. Chips\' Blog'], $defaultContent);
    }

    private function generateExcerpt($content, $length = 150) {
        $text = strip_tags($content);
        $text = preg_replace('/\s+/', ' ', $text);

        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }
}

// Run the builder
if (php_sapi_name() === 'cli') {
    $builder = new BlogBuilder();
    $builder->build();
} else {
    echo "This script should be run from the command line.";
}
?>
