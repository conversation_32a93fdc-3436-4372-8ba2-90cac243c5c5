# Quick Reference Card

## 🚀 Essential Commands

### Create New Post
```bash
php new-post.php "Post Title" category
```

### Build Site
```bash
php build.php
```

### Validate Content
```bash
php validate-content.php
```

## 📝 Post Template

```markdown
---
title: "Your Post Title"
date: 2025-01-15
author: "A. A. Chips"
tags:
  - category
excerpt: "Brief description for SEO"
---

# Your Post Title

Your content here...
```

## 🗂️ Categories

- `advocacy` - Social justice content
- `kitchen` - Recipes and cooking
- `alienation` - Family experiences
- `climate` - Environmental topics
- `humor` - Funny content
- `inspiration` - Inspirational content
- `journal` - Personal entries
- `personal` - Personal stories
- `writings` - Creative writing

## 📁 Key Files

- `config.php` - Site configuration
- `content/` - Your Markdown files
- `generated/` - Generated PHP files
- `includes/` - Template parts
- `css/style.css` - Styling

## 🔧 Common Issues

**Missing frontmatter**: Start with `---` and end with `---`
**Missing fields**: Include `title`, `date`, `author`
**File exists**: Choose different title or delete existing
**Build errors**: Run `php validate-content.php` first

## 💡 Quick Tips

- Use descriptive titles for SEO
- Write compelling excerpts
- Add relevant tags
- Place images in `img/` folder
- Run validation before building
- Templates update all pages automatically

---

For full documentation, see `README.md`
