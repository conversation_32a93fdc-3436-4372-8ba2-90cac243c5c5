/* Base styles */
:root {
  --primary-color: #6b5b95;
  --secondary-color: #88b04b;
  --accent-color: #f7786b;
  --text-color: #333;
  --background-color: #f9f9f9;
  --card-background: #fff;
  --header-background: #6b5b95;
  --footer-background: #444;
  --link-color: #6b5b95;
  --link-hover-color: #88b04b;
  --tag-background: #eee;
  --border-radius: 8px;
  --box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: 'Open Sans', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

img {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: block;
  margin: 1rem auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: var(--primary-color);
}

h1 {
  font-size: 2.5rem;
  border-bottom: 2px solid var(--secondary-color);
  padding-bottom: 0.3em;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

a {
  color: var(--link-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--link-hover-color);
  text-decoration: underline;
}

p {
  margin-bottom: 1.5em;
}

blockquote {
  border-left: 4px solid var(--secondary-color);
  padding-left: 1em;
  margin-left: 0;
  font-style: italic;
  color: #666;
}

.content {
  background-color: white;
  color: #333;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
  text-align: left;
}

.content h1 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.content a {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: var(--border-radius);
  text-decoration: none;
  transition: var(--transition);
}

.content a:hover {
  background-color: #5a4a82;
}

code {
  background-color: #f5f5f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.content-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-top: 2rem;
}

main {
  max-width: 1000px;
  margin: 2rem auto;
  padding: 1rem;
}

.sidebar {
  width: 300px;
  padding-left: 1rem;
  border-left: 1px solid #eee;
}

/* Header */
header {
  background-color: var(--header-background);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}


.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.site-title {
  font-size: 1.8rem;
  margin: 0;
}

.site-title a {
  color: white;
  text-decoration: none;
}

/* Navigation */
nav ul {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
}

nav li {
  margin-left: 1.5rem;
}

nav a {
  color: white;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

nav a:hover {
  color: var(--secondary-color);
  text-decoration: none;
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

/* Cards */
.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 2rem;
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-content {
  padding: 1.5rem;
}

.card-title {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.card-excerpt {
  margin-bottom: 1rem;
  color: #666;
}

.card-meta {
  font-size: 0.9rem;
  color: #888;
  margin-bottom: 0.5rem;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.tag {
  background-color: var(--tag-background);
  border-radius: 20px;
  padding: 0.3rem 0.8rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  transition: var(--transition);
}

.tag:hover {
  background-color: var(--secondary-color);
  color: white;
}

/* Gallery */
.gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  grid-gap: 1rem;
  margin: 2rem 0;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.gallery-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition);
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0,0,0,0.7);
  color: white;
  padding: 0.5rem;
  transform: translateY(100%);
  transition: var(--transition);
}

.gallery-item:hover .gallery-caption {
  transform: translateY(0);
}

/* Music Player */
.music-player {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.playlist {
  list-style: none;
  padding: 0;
  margin: 1rem 0 0 0;
}

.playlist-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: var(--transition);
}

.playlist-item:hover {
  background-color: #f5f5f5;
}

.playlist-item.active {
  background-color: #f0f0f0;
  font-weight: bold;
}

.playlist-item-number {
  width: 30px;
  text-align: center;
  color: #888;
}

.playlist-item-title {
  flex: 1;
}

.playlist-item-duration {
  color: #888;
  font-size: 0.9rem;
}

.player-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1rem;
}

.player-progress {
  height: 5px;
  background-color: #eee;
  border-radius: 5px;
  margin-top: 1rem;
  overflow: hidden;
}

.player-progress-bar {
  height: 100%;
  background-color: var(--secondary-color);
  width: 0%;
}

/* Footer */
footer {
  background-color: var(--footer-background);
  color: white;
  padding: 2rem 0;
  margin-top: 3rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.footer-section {
  flex: 1;
  min-width: 250px;
  margin-bottom: 1.5rem;
}

.footer-section h3 {
  color: white;
  border-bottom: 2px solid var(--secondary-color);
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #ccc;
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 1.5rem;
  margin-top: 1.5rem;
  border-top: 1px solid #555;
  font-size: 0.9rem;
  color: #aaa;
}

.skip-link {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus {
  position: fixed;
  top: 10px;
  left: 10px;
  width: auto;
  height: auto;
  padding: 10px;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
  border: 2px solid #000;
  background-color: #fff;
  color: #000;
  font-size: 1.25rem;
  z-index: 1000;
}

          /* #related, #about, #donate, #categories, #connect */
#related {
  margin: 1rem;
  padding: 1rem;
}


/* Responsive */
@media (max-width: 992px) {
  .content-wrapper {
    flex-direction: column;
  }

  .main-content {
    padding-right: 0;
  }

  .sidebar {
    width: 100%;
    padding-left: 0;
    border-left: none;
    margin-top: 2rem;
  }
}

/* Modal Styles */
.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: var(--card-background);
  margin: 5% auto;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  position: absolute;
  top: 15px;
  right: 20px;
  cursor: pointer;
  transition: var(--transition);
}

.close:hover,
.close:focus {
  color: var(--primary-color);
}

.humor-list {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.humor-item {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  cursor: pointer;
  transition: var(--transition);
  border: 1px solid #eee;
}

.humor-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.humor-item h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.humor-excerpt {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.humor-meta {
  font-size: 0.8rem;
  color: #888;
  display: flex;
  gap: 1rem;
}

.humor-content .author,
.humor-content .date {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.content-body {
  margin: 1.5rem 0;
  line-height: 1.6;
}

.back-btn {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  margin-top: 1rem;
}

.back-btn:hover {
  background-color: #7a9e42;
}

/* Post Header Styles */
.post-meta {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
}

.post-author {
  font-weight: 600;
}

.post-date {
  color: #888;
}

.post-excerpt {
  background-color: #f8f9fa;
  border-left: 4px solid var(--secondary-color);
  padding: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #555;
}

.post-tags {
  margin: 1rem 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.post-tags .tag {
  background-color: var(--primary-color);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.post-separator {
  border: none;
  height: 2px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  margin: 2rem 0;
  border-radius: 1px;
}

@media (max-width: 768px) {
  .modal-content {
    margin: 10% auto;
    width: 95%;
    padding: 1.5rem;
  }

  .header-container {
    flex-direction: column;
    align-items: flex-start;
  }

  nav {
    margin-top: 1rem;
    width: 100%;
  }

  nav ul {
    flex-direction: column;
  }

  nav li {
    margin-left: 0;
    margin-bottom: 0.5rem;
  }

  .mobile-menu-toggle {
    display: block;
    position: absolute;
    top: 1rem;
    right: 1rem;
  }

  .nav-menu {
    display: none;
  }

  .nav-menu.active {
    display: block;
  }

  .gallery {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .footer-section {
    flex: 100%;
  }
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: #f0f0f0;
    --background-color: #222;
    --card-background: #333;
    --header-background: #444;
    --footer-background: #111;
    --tag-background: #444;
    --link-color: #a991e5;
    --link-hover-color: #c2e59c;
  }

  code {
    background-color: #444;
  }

  .card {
    border: 1px solid #444;
  }

  .playlist-item:hover {
    background-color: #444;
  }

  .playlist-item.active {
    background-color: #555;
  }

  .player-progress {
    background-color: #444;
  }
}
