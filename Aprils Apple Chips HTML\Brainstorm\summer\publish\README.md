# A. A. Chips' Enhanced Blog System

A simple, powerful blog system that converts Markdown files with YAML frontmatter into dynamic PHP pages with templating and configuration management.

## 🚀 Quick Start

### 1. Create a New Post
```bash
php new-post.php "My Amazing Post" journal
```

### 2. Edit Your Post
Open the created file in `content/journal/my-amazing-post.md` and add your content.

### 3. Build the Site
```bash
php build.php
```

### 4. View Your Post
Open `generated/journal/my-amazing-post.php` in your web browser.

## 📝 Writing Posts

### Basic Post Structure
```markdown
---
title: "Your Post Title"
date: 2025-01-15
author: "A. A. Chips"
tags:
  - journal
  - personal
excerpt: "A brief description that appears in previews and search results"
---

# Your Post Title

Write your content here using Markdown syntax.

## Section Heading

- Bullet points work
- **Bold text** and *italic text*
- [Links](https://example.com)

![Image description](img/your-image.jpg)
```

### Required Fields
- **title**: The post title (appears in browser tab and page header)
- **date**: Publication date in YYYY-MM-DD format
- **author**: Author name (usually "A. A. Chips")

### Optional Fields
- **tags**: Categories for your post (advocacy, journal, personal, etc.)
- **excerpt**: Brief description for SEO and post previews
- **reading_time**: Estimated reading time in minutes

## 🗂️ Content Organization

### Available Categories
Create posts in these categories by using the category name with `new-post.php`:

- **advocacy** - Street advocacy and social justice content
- **kitchen** - Apple Chip Kitchen recipes and cooking
- **alienation** - Family alienation experiences
- **climate** - Climate and environmental topics
- **humor** - Funny content (appears in modal)
- **inspiration** - Inspirational content
- **journal** - Personal journal entries
- **personal** - Personal stories and experiences
- **writings** - Creative writing and essays

### File Organization
```
content/
├── advocacy/
│   └── my-advocacy-post.md
├── journal/
│   └── my-journal-entry.md
├── personal/
│   └── my-story.md
└── general-post.md          # Posts without specific category
```

## 🛠️ Commands

### Creating New Posts
```bash
# Create a journal post
php new-post.php "My Daily Thoughts" journal

# Create an advocacy post
php new-post.php "Fighting for Justice" advocacy

# Create a general post (no category)
php new-post.php "Random Thoughts"
```

### Building the Site
```bash
# Build all posts
php build.php
```

### Validating Content
```bash
# Check all posts for errors
php validate-content.php
```

## ⚙️ Configuration

Edit `config.php` to customize your site:

### Site Information
```php
'site' => [
    'name' => 'A. A. Chips',
    'title' => 'A. A. Chips\' Blog',
    'description' => 'Your site description here...',
    'author' => 'A. A. Chips',
]
```

### Navigation Menu
```php
'navigation' => [
    'Home' => 'index.html',
    'About' => 'about.html',
    'Contact' => 'contact.html',
]
```

### Categories
```php
'categories' => [
    'My Category' => 'my-category.html',
    'Another Category' => 'another.html',
]
```

## 🎨 Customizing Appearance

### CSS Classes for Styling
The system generates structured HTML with these CSS classes:

```css
/* Post header styling */
.post-header { }
.post-title { }
.post-meta { }
.post-author { }
.post-date { }
.post-excerpt { }

/* Tags styling */
.post-tags { }
.tags-label { }
.tag { }

/* Content styling */
.post-content { }
```

Add these styles to `css/style.css` to customize the appearance.

## 📁 File Structure

```
├── README.md                    # This file
├── config.php                  # Site configuration
├── build.php                   # Build system
├── new-post.php                # Create new posts
├── validate-content.php        # Validate content
├── page template.htm           # Main page template
├── includes/                   # Template parts
│   ├── header.php             # Site header
│   ├── sidebar.php            # Sidebar content
│   └── footer.php             # Site footer
├── content/                    # Your Markdown files
│   ├── advocacy/
│   ├── journal/
│   └── ...
├── generated/                  # Generated PHP files
├── css/                       # Stylesheets
├── js/                        # JavaScript files
└── img/                       # Images
```

## 🔧 Troubleshooting

### Common Issues

**"No valid YAML frontmatter found"**
- Make sure your post starts with `---` and ends with `---`
- Check that there are no spaces before the opening `---`

**"Missing required field"**
- Ensure your post has `title`, `date`, and `author` fields
- Check the date format is YYYY-MM-DD

**"File already exists"**
- The post filename already exists
- Choose a different title or delete the existing file

### Getting Help

1. **Validate your content**: Run `php validate-content.php`
2. **Check the build output**: Look for error messages when running `php build.php`
3. **Verify file structure**: Make sure files are in the correct directories

## 📋 Workflow Example

Here's a typical workflow for creating and publishing a new post:

1. **Create the post**:
   ```bash
   php new-post.php "My Weekend Adventure" personal
   ```

2. **Edit the content**:
   - Open `content/personal/my-weekend-adventure.md`
   - Update the excerpt and add your content
   - Save the file

3. **Validate** (optional):
   ```bash
   php validate-content.php
   ```

4. **Build the site**:
   ```bash
   php build.php
   ```

5. **View your post**:
   - Open `generated/personal/my-weekend-adventure.php` in your browser

## 🌟 Tips for Better Posts

- **Write compelling excerpts** - They appear in search results and previews
- **Use descriptive titles** - They help with SEO and navigation
- **Add relevant tags** - They help organize and categorize content
- **Include images** - Place them in the `img/` folder and reference them
- **Break up long content** - Use headings and bullet points for readability

## 🔄 Updating the System

When you make changes to:
- **Templates** (`includes/` files): Changes apply to all posts immediately
- **Configuration** (`config.php`): Changes apply to all posts immediately
- **Content** (`.md` files): Run `php build.php` to regenerate
- **Styles** (`css/style.css`): Changes apply immediately

---

**Happy blogging!** 🎉
