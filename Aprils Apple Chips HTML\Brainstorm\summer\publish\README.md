# Markdown Blog

A simple blog system that parses Markdown files into HTML and displays them with a clean, responsive design.

## Features

- Parses Markdown files into HTML
- Supports Obsidian-style internal links, hashtags, and metadata
- Responsive design with Bootstrap
- Sidebar with widgets (tags, recent posts, projects)
- Image galleries
- Comment sections
- Search functionality
- Tag-based navigation

## Directory Structure

- `/content`: Where you add your Markdown files
- `/public`: Contains the compiled HTML files and static assets
- `/static`: Where you add your multimedia content (images, videos, etc.)
- `/static/img`: Where you add your images
- `/templates`: Contains the EJS templates for the blog

## Getting Started

1. Install Node.js if you haven't already
2. Clone this repository
3. Run `npm install` to install dependencies
4. Add your Markdown files to the `/content` directory
5. Add your images and other multimedia content to the `/static` directory
6. Run `npm start` to start the server
7. Visit `http://localhost:3000` in your browser

## Adding Content

### Markdown Files

Add your Markdown files to the `/content` directory. Each file should have a `.md` extension and can include front matter metadata at the top of the file:

```markdown
---
title: Your Post Title
date: 2023-05-15
tags: [tag1, tag2, tag3]
excerpt: A brief excerpt of your post
---

Your post content goes here...
```

### Images and Multimedia

Add your images and other multimedia content to the `/static` directory. You can reference them in your Markdown files like this:

```markdown
![Alt text](/static/img/your-image.jpg)
```

### Internal Links

You can create internal links to other posts using the Obsidian-style double bracket syntax:

```markdown
[[Post Title]]
```

Or with a custom link text:

```markdown
[[Post Title|Custom Link Text]]
```

### Hashtags

You can use hashtags in your Markdown files to categorize your posts:

```markdown
This post is about #markdown and #blogging.
```

## Customization

### Templates

The templates are located in the `/templates` directory. You can modify them to change the appearance of your blog.

### Styles

The CSS styles are located in the `/public/css/style.css` file. You can modify them to change the appearance of your blog.

### JavaScript

The JavaScript code is located in the `/public/js/script.js` file. You can modify it to add custom functionality to your blog.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
