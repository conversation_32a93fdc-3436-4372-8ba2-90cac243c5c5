<?php
// Debug the build process for one file
$file = 'content/hadestown-review.md';
$content = file_get_contents($file);

// Parse markdown
if (preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
    $frontmatter = [];
    $yamlLines = explode("\n", $matches[1]);
    $currentKey = null;
    
    foreach ($yamlLines as $line) {
        $line = trim($line);
        if (empty($line)) continue;
        
        // Handle array items
        if (strpos($line, '-') === 0) {
            if ($currentKey && !isset($frontmatter[$currentKey])) {
                $frontmatter[$currentKey] = [];
            }
            if ($currentKey) {
                $frontmatter[$currentKey][] = trim(substr($line, 1));
            }
        } elseif (strpos($line, ':') !== false) {
            [$key, $value] = explode(':', $line, 2);
            $key = trim($key);
            $value = trim($value);
            $currentKey = $key;
            
            if (!empty($value)) {
                $frontmatter[$key] = $value;
            }
        }
    }
    
    echo "Frontmatter:\n";
    print_r($frontmatter);
    
    // Generate post header
    $header = '';
    
    if (!empty($frontmatter)) {
        // Add title
        if (isset($frontmatter['title'])) {
            $header .= "<h1>{$frontmatter['title']}</h1>\n";
        }
        
        // Add post metadata
        $metadata = [];
        if (isset($frontmatter['author'])) {
            $metadata[] = "<span class=\"post-author\">By {$frontmatter['author']}</span>";
        }
        if (isset($frontmatter['date'])) {
            $metadata[] = "<span class=\"post-date\">{$frontmatter['date']}</span>";
        }
        
        if (!empty($metadata)) {
            $header .= "<div class=\"post-meta\">" . implode(' • ', $metadata) . "</div>\n";
        }
        
        // Add excerpt if available
        if (isset($frontmatter['excerpt'])) {
            $header .= "<div class=\"post-excerpt\"><em>{$frontmatter['excerpt']}</em></div>\n";
        }
        
        // Add tags if available
        if (isset($frontmatter['tags'])) {
            $tags = $frontmatter['tags'];
            if (is_array($tags) && !empty($tags)) {
                $header .= "<div class=\"post-tags\">";
                foreach ($tags as $tag) {
                    $header .= "<span class=\"tag\">{$tag}</span>";
                }
                $header .= "</div>\n";
            } elseif (is_string($tags)) {
                $header .= "<div class=\"post-tags\"><span class=\"tag\">{$tags}</span></div>\n";
            }
        }
        
        // Add separator
        $header .= "<hr class=\"post-separator\">\n\n";
    }
    
    echo "\nGenerated header:\n";
    echo $header;
    echo "\n\nHeader length: " . strlen($header) . "\n";
}
?>
