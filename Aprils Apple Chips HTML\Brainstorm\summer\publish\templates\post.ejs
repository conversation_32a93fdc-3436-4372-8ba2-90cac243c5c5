<%- include('partials/header', { title: post.title }) %>

<article class="blog-post">
  <header class="mb-4">
    <h1 class="blog-post-title"><%= post.title %></h1>
    <div class="blog-post-meta text-muted">
      <%= post.date %>
      <% if (post.tags && post.tags.length > 0) { %>
        • 
        <% post.tags.forEach((tag, index) => { %>
          <a href="/tag/<%= tag %>" class="badge bg-secondary text-decoration-none">#<%= tag %></a><%= index < post.tags.length - 1 ? ' ' : '' %>
        <% }); %>
      <% } %>
    </div>
  </header>

  <div class="blog-post-content">
    <%- post.content %>
  </div>
</article>

<div class="card mt-5">
  <div class="card-header">Comments</div>
  <div class="card-body">
    <div class="comments">
      <p class="text-muted">Comments are coming soon!</p>
    </div>
    <form class="mt-4">
      <div class="mb-3">
        <label for="name" class="form-label">Name</label>
        <input type="text" class="form-control" id="name" placeholder="Your name">
      </div>
      <div class="mb-3">
        <label for="email" class="form-label">Email</label>
        <input type="email" class="form-control" id="email" placeholder="Your email">
      </div>
      <div class="mb-3">
        <label for="comment" class="form-label">Comment</label>
        <textarea class="form-control" id="comment" rows="3" placeholder="Your comment"></textarea>
      </div>
      <button type="submit" class="btn btn-primary">Submit Comment</button>
    </form>
  </div>
</div>

<%- include('partials/footer') %>
