<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - A. A. Chips' Blog System</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    
    <header>
        <div class="container">
            <a href="index.html">A. A. Chips</a>
            <nav class="nav">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="advocacy.html">Advocacy</a></li>
                    <li><a href="personal.html">Personal</a></li>
                    <li><a href="gallery.html">Gallery</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main id="main-content" tabindex="-1">
        <div class="content">
            <h1>Blog System Demo</h1>
            <p>Welcome to the A. A. Chips simple blog system! This demonstrates the key features:</p>
            
            <h2>Features Demonstrated</h2>
            
            <div class="card">
                <div class="card-content">
                    <h3>✅ Markdown to HTML Conversion</h3>
                    <p>All your markdown files have been converted to HTML pages in the <code>generated/</code> folder.</p>
                    <p><strong>Example:</strong> <a href="generated/hadestown-review.html">Hadestown Review</a></p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content">
                    <h3>✅ Modal System for Humor Content</h3>
                    <p>Shorthand content (humor posts) are displayed in modals instead of separate pages.</p>
                    <p><strong>Try it:</strong> <button onclick="showHumorModal()" class="back-btn">Open Humor Modal</button></p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content">
                    <h3>✅ Component-Based Templates</h3>
                    <p>The system uses PHP includes for modular templates:</p>
                    <ul>
                        <li><code>includes/header.php</code> - Site header</li>
                        <li><code>includes/sidebar.php</code> - Sidebar with navigation</li>
                        <li><code>includes/footer.php</code> - Site footer</li>
                    </ul>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content">
                    <h3>✅ JSON Data Generation</h3>
                    <p>Humor content has been compiled to JSON for easy modal loading.</p>
                    <p><strong>Check:</strong> <a href="data/humor.json">humor.json</a> (3 items generated)</p>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content">
                    <h3>✅ Simple Build Process</h3>
                    <p>One command generates all HTML files:</p>
                    <code>php build.php</code> or run <code>build.bat</code>
                </div>
            </div>
            
            <h2>Generated Content</h2>
            <p>Your blog now has:</p>
            <ul>
                <li><strong>45+ HTML pages</strong> generated from markdown files</li>
                <li><strong>3 humor items</strong> compiled to JSON for modal display</li>
                <li><strong>Main index page</strong> using your existing content</li>
                <li><strong>Responsive design</strong> with your existing CSS</li>
            </ul>
            
            <h2>Next Steps</h2>
            <ol>
                <li>Customize the templates in <code>includes/</code></li>
                <li>Add more content to <code>content/</code></li>
                <li>Run <code>build.php</code> to regenerate</li>
                <li>Upload to your web server</li>
            </ol>
        </div>
    </main>

    <section id="related">
        <div>
            <h2>Related Posts</h2>
            <div class="grid">
                <article>
                    <h3><a href="generated/hadestown-review.html">Hadestown Review</a></h3>
                    <p>A review of the teen edition performance.</p>
                </article>
                <article>
                    <h3><a href="generated/survivor-manifesto.html">Survivor Manifesto</a></h3>
                    <p>Personal reflections on survival and resilience.</p>
                </article>
            </div>
        </div>
        <div>
            <section id="about">
                <h3>About A. A. Chips</h3>
                <p>Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.</p>
            </section>

            <section id="donate">
                <h3>Donate</h3>
                <p>If you like what I am doing. Or if you hate what I am doing. You can donate one-time or recurring pledges through my <a href="https://www.ko-fi.com/aachips">Ko-Fi page</a>.</p>
                <a href="crowdfund.html" target="_blank">What am I raising funds for?</a>
            </section>
            
            <section id="categories">
                <h3>Categories</h3>
                <ul>
                    <li><a href="advocacy.html">Street Advocacy</a></li>
                    <li><a href="kitchen.html">Apple Chip Kitchen</a></li>
                    <li><a href="alienation.html">Alienation</a></li>
                    <li><a href="climate.html">Climate</a></li>
                    <li><a href="#" onclick="showHumorModal()">Humor</a></li>
                    <li><a href="inspiration.html">Inspiration</a></li>
                    <li><a href="journal.html">Journal</a></li>
                    <li><a href="personal.html">Personal Stories</a></li>
                    <li><a href="writings.html">Writings</a></li>
                </ul>
            </section>
            
            <section id="connect">
                <h3>Connect</h3>
                <p>Share your thoughts, questions, or just say hello. Use the chat widget below. Leave an email address to reply to.</p>
            </section>
        </div>
    </section>
    
    <footer>
        <div class="container">
            <div class="bottom">
                <p>© 2025 A. A. Chips. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <!-- Modal for shorthand content -->
    <div id="contentModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>
    
    <script src="js/script.js"></script>
    <script src="js/modal.js"></script>
</body>
</html>
