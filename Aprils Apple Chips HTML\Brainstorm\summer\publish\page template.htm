<!DOCTYPE html>
<html lang="<?php echo $config['site']['language'] ?? 'en'; ?>">

<head>
    <meta charset="<?php echo $config['site']['charset'] ?? 'UTF-8'; ?>">
    <meta name="viewport" content="<?php echo $config['meta']['viewport'] ?? 'width=device-width, initial-scale=1.0'; ?>">

    <!-- Title -->
    <title><?php
        if (isset($page_title)) {
            echo htmlspecialchars($page_title) . ' - ' . htmlspecialchars($config['site']['title'] ?? 'A. A. Chips\' Blog');
        } else {
            echo htmlspecialchars($config['site']['title'] ?? 'A. A. Chips\' Blog');
        }
    ?></title>

    <!-- Meta Description -->
    <meta name="description" content="<?php
        echo htmlspecialchars($meta_description ?? $config['site']['description'] ?? '');
    ?>">

    <!-- Meta Keywords -->
    <meta name="keywords" content="<?php
        echo htmlspecialchars($meta_keywords ?? $config['meta']['keywords'] ?? '');
    ?>">

    <!-- Author -->
    <meta name="author" content="<?php echo htmlspecialchars($config['site']['author'] ?? 'A. A. Chips'); ?>">

    <!-- Robots -->
    <meta name="robots" content="<?php echo $config['meta']['robots'] ?? 'index, follow'; ?>">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?php echo ($css_path ?? $config['paths']['css'] ?? 'css/') . 'style.css'; ?>">

    <!-- External Services -->
    <?php if ($config['social']['chatway_enabled'] ?? false): ?>
    <!-- Chatway -->
    <?php endif; ?>

    <?php if ($config['social']['clarity_enabled'] ?? false): ?>
    <!-- Microsoft Clarity -->
    <?php endif; ?>
</head>

  <body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <?php include 'includes/header.php'; ?>

      <main id="main-content" tabindex="-1">
          <div class="content">
              <?php if (isset($content)) echo $content; ?>
          </div>
      </main>

      <?php include 'includes/sidebar.php'; ?>
      <?php include 'includes/footer.php'; ?>

      <!-- Modal for shorthand content -->
      <div id="contentModal" class="modal" style="display: none;">
          <div class="modal-content">
              <span class="close">&times;</span>
              <div id="modalBody"></div>
          </div>
      </div>

      <script src="<?php echo isset($js_path) ? $js_path : 'js/'; ?>script.js"></script>
      <script src="<?php echo isset($js_path) ? $js_path : 'js/'; ?>modal.js"></script>
  </body>
</html>
