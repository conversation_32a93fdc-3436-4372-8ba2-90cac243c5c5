<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'The Eco-Sattva Vows';
$meta_description = '"I vow to myself and to each of you: To commit myself daily to the healing of the world\\t\\tAnd the welfare of all beings.\\tTo live on earth more lightly and less violently \\t\\tin the food, products, and energy I consume."';
$meta_keywords = 'climate, advocacy, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'The Eco-Sattva Vows',
  'author' => '<PERSON> & <PERSON>',
  'tags' => 
  array (
    0 => 'climate',
    1 => 'advocacy',
  ),
  'excerpt' => '"I vow to myself and to each of you: To commit myself daily to the healing of the world\\t\\tAnd the welfare of all beings.\\tTo live on earth more lightly and less violently \\t\\tin the food, products, and energy I consume."',
  'categories' => 
  array (
    0 => 'Climate',
  ),
);

// Raw content
$post_content = '<h2>The Ecosattva Vows</h2>

<img src="static/img/ecosattva.jpg">

<p>from <em>Active hope: How to Face the Mess We\'re in without Going Crazy</em>, by Joanna Macy and Chris Johnstone</p>

<p>"I vow to myself and to each of yoU:</p>

<p>To commit myself daily to the healing of the world</p>
<p>And the welfare of all beings.</p>

<p>To live on earth more lightly and less violently</p>
<p>in the food, products, and energy I consume.</p>

<p>To draw strength and guidance from the living Earth,</p>
<p>the ancestors, the future generations,</p>
<p>and my brothers and sisters of all species.</p>

<p>To support others in our work for the world</p>
<p>and to ask for help when I need it.</p>

<p>To pursue a daily practice that clarifies the mind,</p>
<p>strengthens my heart, and supports me in observing these vows."</p>

<p><em>for memes about [[Throwing Car Batteries In the Ocean - Memes Collection|throwing car batteries in the ocean, please go here]]</em></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>