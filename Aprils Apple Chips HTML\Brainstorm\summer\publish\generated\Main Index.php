<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = '"Main Index"';
$meta_description = '"Main navigation page for the blog content"';
$meta_keywords = 'navigation, index, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => '"Main Index"',
  'date' => '"2025-05-20"',
  'tags' => 
  array (
    0 => 'navigation',
    1 => 'index',
  ),
  'excerpt' => '"Main navigation page for the blog content"',
);

// Raw content
$post_content = '<h1>Main Index</h1>

<h2>Advocacy & Community</h2>
<ul><li>[[Bugs and Addiction]]</li>
<p><li>[[city-council-letter]]</li></p>
<p><li>[[Cultivating Community and Solidarity]]</li></p>
<p><li>[[Hobo Code of Ethics]]</li></p>
<p><li>[[Responding to Panhandlers]]</li></p>
<p><li>[[wellness-checks]]</li></p>

<h2>Health & Wellbeing</h2>
<p><li>[[How to Handle a Dissociative Episode]]</li></p>
<p><li>[[Should Social Workers Diagnose Mental Illness]]</li></p>
<p><li>[[Traumatic Brain Injury in Homeless People is Underrecognized - NeurologyToday]]</li></p>
<p><li>[[US Surgeon General Releases New Framework for Mental Health _ Well-being in the Workplace 102022 Press Release HHS]]</li></p>

<h2>Environmental Topics</h2>
<p><li>[[Why Possums]]</li></p>
<p><li>[[The Swannanoa Mulch Fire]]</li></p>
<p><li>[[Rethinking Digital Ecosystems - A Call for Ecological Literacy in Tech]]</li></p>
<p><li>[[If you have never been in a hurricane - Nappy Thoughts]]</li></p>

<h2>Personal Reflections</h2>
<p><li>[[If you choose to be homeless, it means your choices in the situation were terrible 1]]</li></p>
<p><li>[[stay-warm]]</li></ul></p>

<img src="img/possumfriends.png" alt="possumfriends.png">

<p>---</p>

<p><em>This index page has been reorganized to group related content. More sections will be added as content is further organized.</em></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>