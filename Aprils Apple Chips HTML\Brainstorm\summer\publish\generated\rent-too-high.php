<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'Is the Rent Too Damn High?';
$meta_description = 'To comfortably afford that rent using the 30% rule, we\'d need a minimum wage of... *drumroll please*... $37.06 an hour!';
$meta_keywords = 'CompassionateCities, advocacy, disasterrelief, homeless, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'Is the Rent Too Damn High?',
  'excerpt' => 'To comfortably afford that rent using the 30% rule, we\'d need a minimum wage of... *drumroll please*... $37.06 an hour!',
  'tags' => 
  array (
    0 => 'CompassionateCities',
    1 => 'advocacy',
    2 => 'disasterrelief',
    3 => 'homeless',
  ),
);

// Raw content
$post_content = '<p>Hey there, budget warriors and paycheck perceivers! Ever wonder if that minimum wage grind can actually get you an apartment that doesn\'t resemble a shoebox? Buckle up, because we\'re diving into the wild world of rent and minimum wage!</p>

<p>The age-old question: how much moolah should you realistically shell out for that roof over your head? The general rule of thumb is 30% of your income. But can minimum wage actually swing that in the land of sky-high rent?</p>

<p>Let\'s play a little game of Rent Roulette! We\'ll take Uncle Sam\'s current minimum wage (<em>spins the wheel</em>) and see where it lands us rent-wise. As of today, that wage is around $7.25 an hour. Yikes!</p>

<p>According to the rent gods (a.k.a. Zillow), the average rent for a one-bedroom apartment in the US is roughly $1,927. Yikes again!</p>

<h3>Here\'s the math breakdown (brace yourselves):</h3>

<p>To comfortably afford that rent using the 30% rule, we\'d need a minimum wage of... <em>drumroll please</em>... $37.06 an hour!</p>

<h3>So, what does this mean?</h3>

<p>Well, for folks in some areas with sky-high rent, the current minimum wage might leave them needing a roommate... or a time machine to a more affordable era.</p>

<h3>But wait, there\'s more!</h3>

<p>This is just a national average, and rent prices can vary wildly depending on where you live. San Francisco might have you sleeping in a closet, while Des Moines might offer a palace for peanuts (relatively speaking).</p>

<h3>The bottom line?</h3>

<p>Minimum wage and rent is a complex beast. A national increase might need adjustments based on location. Plus, there\'s the whole impact-on-businesses thing to consider. Economists are like the weather forecasters of this debate – never quite sure what storm is brewing.</p>

<h3>What do you think? Should minimum wage reflect the cost of rent? Share your thoughts in the comments below!</h3>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>