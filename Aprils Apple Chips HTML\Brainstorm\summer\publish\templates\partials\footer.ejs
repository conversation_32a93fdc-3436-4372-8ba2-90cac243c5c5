      </div>
      <div class="col-md-4">
        <div class="sidebar">
          <div class="card mb-4">
            <div class="card-header">About</div>
            <div class="card-body">
              <p>Welcome to my blog! This is where I share my thoughts, ideas, and stories.</p>
            </div>
          </div>
          
          <div class="card mb-4">
            <div class="card-header">Tags</div>
            <div class="card-body">
              <div class="tags">
                <% tags.forEach(tag => { %>
                  <a href="/tag/<%= tag %>" class="badge bg-secondary text-decoration-none me-1 mb-1">#<%= tag %></a>
                <% }); %>
              </div>
            </div>
          </div>
          
          <div class="card mb-4">
            <div class="card-header">Recent Posts</div>
            <div class="card-body">
              <ul class="list-unstyled">
                <% posts.slice(0, 5).forEach(post => { %>
                  <li class="mb-2">
                    <a href="/posts/<%= post.slug %>" class="text-decoration-none"><%= post.title %></a>
                  </li>
                <% }); %>
              </ul>
            </div>
          </div>
          
          <div class="card mb-4">
            <div class="card-header">Projects</div>
            <div class="card-body">
              <div class="project-ad mb-3">
                <h5>Project 1</h5>
                <p>Description of Project 1</p>
                <a href="#" class="btn btn-sm btn-primary">Learn More</a>
              </div>
              <div class="project-ad">
                <h5>Project 2</h5>
                <p>Description of Project 2</p>
                <a href="#" class="btn btn-sm btn-primary">Learn More</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <footer class="bg-dark text-white py-4 mt-4">
    <div class="container">
      <div class="row">
        <div class="col-md-6">
          <h5>Markdown Blog</h5>
          <p>A simple blog built with Node.js, Express, and Markdown</p>
        </div>
        <div class="col-md-6 text-md-end">
          <p>&copy; <%= new Date().getFullYear() %> All Rights Reserved</p>
        </div>
      </div>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="/js/script.js"></script>
</body>
</html>
