const fs = require('fs-extra');
const path = require('path');

// Paths
const sourceDir = path.join(__dirname, '..', 'public');
const contentDir = path.join(__dirname, 'content');
const staticDir = path.join(__dirname, 'static', 'img');

// Ensure directories exist
fs.ensureDirSync(contentDir);
fs.ensureDirSync(staticDir);

// Function to copy markdown files
async function copyMarkdownFiles() {
  try {
    // Get all directories in the source directory
    const dirs = await fs.readdir(sourceDir);
    
    let filesCopied = 0;
    
    // Process each directory
    for (const dir of dirs) {
      const dirPath = path.join(sourceDir, dir);
      const stats = await fs.stat(dirPath);
      
      // Skip if not a directory
      if (!stats.isDirectory()) continue;
      
      // Get all markdown files in the directory
      try {
        const files = await fs.readdir(dirPath);
        const mdFiles = files.filter(file => file.endsWith('.md'));
        
        // Copy each markdown file
        for (const file of mdFiles) {
          const sourcePath = path.join(dirPath, file);
          const destPath = path.join(contentDir, `${dir}-${file}`);
          
          await fs.copy(sourcePath, destPath);
          filesCopied++;
          
          console.log(`Copied ${sourcePath} to ${destPath}`);
        }
      } catch (err) {
        console.error(`Error reading directory ${dirPath}:`, err);
      }
    }
    
    console.log(`Copied ${filesCopied} markdown files to ${contentDir}`);
  } catch (err) {
    console.error('Error copying markdown files:', err);
  }
}

// Function to copy image files
async function copyImageFiles() {
  try {
    // Get all directories in the source directory
    const dirs = await fs.readdir(sourceDir);
    
    let filesCopied = 0;
    
    // Process each directory
    for (const dir of dirs) {
      const dirPath = path.join(sourceDir, dir);
      const stats = await fs.stat(dirPath);
      
      // Skip if not a directory
      if (!stats.isDirectory()) continue;
      
      // Get all image files in the directory
      try {
        const files = await fs.readdir(dirPath);
        const imgFiles = files.filter(file => 
          file.endsWith('.jpg') || 
          file.endsWith('.jpeg') || 
          file.endsWith('.png') || 
          file.endsWith('.gif')
        );
        
        // Copy each image file
        for (const file of imgFiles) {
          const sourcePath = path.join(dirPath, file);
          const destPath = path.join(staticDir, `${dir}-${file}`);
          
          await fs.copy(sourcePath, destPath);
          filesCopied++;
          
          console.log(`Copied ${sourcePath} to ${destPath}`);
        }
      } catch (err) {
        console.error(`Error reading directory ${dirPath}:`, err);
      }
    }
    
    console.log(`Copied ${filesCopied} image files to ${staticDir}`);
  } catch (err) {
    console.error('Error copying image files:', err);
  }
}

// Run the functions
async function run() {
  await copyMarkdownFiles();
  await copyImageFiles();
  console.log('Content copying complete!');
}

run();
