<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Title -->
    <title>Alternatives to 'I love you' - A. A. Chips' Blog</title>

    <!-- Meta Description -->
    <meta name="description" content="How to show you care without saying 'I love you'">

    <!-- Meta Keywords -->
    <meta name="keywords" content="memes, alienation, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life">

    <!-- Author -->
    <meta name="author" content="A. A. Chips">

    <!-- Robots -->
    <meta name="robots" content="index, follow">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="../css/style.css">

    <!-- External Services -->
        <!-- Chatway -->
    
        <!-- Microsoft Clarity -->
    </head>

  <body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
    <div class="container">
        <a href="../index.html">
            A. A. Chips        </a>
        <nav class="nav">
            <ul>
                                    <li><a href="../index.html">Home</a></li>
                                    <li><a href="../about.html">About</a></li>
                                    <li><a href="../advocacy.html">Advocacy</a></li>
                                    <li><a href="../personal.html">Personal</a></li>
                                    <li><a href="../gallery.html">Gallery</a></li>
                            </ul>
        </nav>
    </div>
</header>

      <main id="main-content" tabindex="-1">
          <div class="content">
              <article class="post-header">
    <h1 class="post-title">Alternatives to 'I love you'</h1>
    <div class="post-meta"><span class="post-author"><i class="icon-user"></i>By Sue Ellson</span></div>
    <div class="post-excerpt">
        <p><em>How to show you care without saying 'I love you'</em></p>
    </div>
    <div class="post-tags">
        <span class="tags-label">Tags:</span>
        <a href="tag-memes.html" class="tag">memes</a>
        <a href="tag-alienation.html" class="tag">alienation</a>
    </div>
</article>

<div class="post-content">

<p>“The most important thing in this world is to learn to give out love and let it come in.” ~Morrie Schwartz</p>

<p>As a child, I never heard the phrase “I love you.” Now, I hear people say it all the time—at the end of phone calls and whenever parting ways.</p>

<p>When I moved away from my hometown of Adelaide, South Australia, twenty years ago, I noticed how much less I felt loved interstate in Melbourne, Victoria. Even though I didn’t hear “I love you” when I was in Adelaide, somehow I knew people cared.</p>

<p>Soon after I arrived here, I had two wonderful children who’ve taught me all about love. They regularly tell me they love me, and I often overhear them telling their friends.</p>

<p>This got me thinking: how can we <a href="http://tinybuddha.com/blog/50-ways-to-show-you-care-without-spending-a-dime/">let people know we care</a>, beyond simply saying “I love you?”</p>

<p>I decided to make a list of some expressions that we can all say more often to family, friends, partners, and even colleagues. Perhaps you could use one of these each week for the next year.</p>

<ul><li>You are special to me.</li>
<p><li>I feel amazing when I spend time with you.</li></p>
<p><li>You give me goosebumps.</li></p>
<p><li>I feel safe sharing my secrets with you.</li></p>
<p><li>I accept you as you are.</li></p>
<p><li>I understand how you feel.</li></p>
<p><li>Is there anything I can do to help?</li></p>
<p><li>I always have fun when I am with you.</li></p>
<p><li>Please tell me how it is for you so I can understand.</li></p>
<p><li>Can I hold your hand?</li></p>
<p><li>Can I give you a hug?</li></p>
<p><li>You inspire me.</li></p>
<p><li>I really appreciate it when you…</li></p>
<p><li>You are one of the most amazing gifts I have ever received.</li></p>
<p><li>I value everything you’ve taught me.</li></p>
<p><li>The insights you have shared mean the world to me.</li></p>
<p><li>Your thoughtfulness is a delight to receive.</li></p>
<p><li>I will never forget how you…</li></p>
<p><li>I feel so relaxed and happy when you…</li></p>
<p><li>Seeing you when … happened made it all okay.</li></p>
<p><li>I can feel it when your heart sings because it makes my heart sing too.</li></p>
<p><li>I could sit next to you and not say anything and be at peace.</li></p>
<p><li>The way you handled … showed me that you are truly…</li></p>
<p><li>Your comments about … helped me enormously.</li></p>
<p><li>I’m thankful to have you in my life.</li></p>
<p><li>I could go anywhere with you.</li></p>
<p><li>I believe your intentions for me are always good, even when I cannot understand what you do.</li></p>
<p><li>I trust you.</li></p>
<p><li>I can go outside of my comfort zone with you.</li></p>
<p><li>Knowing you gives me courage.</li></p>
<p><li>The world is less scary when I am with you.</li></p>
<p><li>I appreciate that your suggestions help me make difficult choices.</li></p>
<p><li>I lose all concept of time when I am with you.</li></p>
<p><li>If something serious happened to me, you’re the first person I would call.</li></p>
<p><li>You are so generous in spirit.</li></p>
<p><li>Surprise me more often because I like your surprises.</li></p>
<p><li>I love how you … whenever I need to …</li></p>
<p><li>I hear your voice even when we are not in the same place.</li></p>
<p><li>I feel connected to you even when I cannot see you.</li></p>
<p><li>Your wisdom has saved me.</li></p>
<p><li>I feel refreshed and renewed around you.</li></p>
<p><li>I enjoy your sense of humor.</li></p>
<p><li>Whenever I see a photo of us together, I smile.</li></p>
<p><li>I appreciate that you think about my feelings before you do and say things.</li></p>
<p><li>Your smile makes me smile.</li></p>
<p><li>I love that you know me so well.</li></p>
<p><li>When I think about you, I often remember when you…</li></p>
<p><li>I want to keep you in my past, present, and future.</li></p>
<p><li>I can be me when I am with you—I hope you feel the same way.</li></p>
<p><li>Circumstance brought us together; choice keeps us together.</li></p>
<p><li>You are so lovable.</li></p>
<p><li>I love you.</li></ul></p>

<p>I know that the positive feedback I’ve received in the past has kept me going during the darkest moments of my life.</p>

<p>I hope that by saying “I love you” in many different ways, the special people in your life will have good memories that can sustain them during the more difficult moments in their lives.</p>

<p>How do you let people know you love them?</p>

</div><!-- .post-content -->
          </div>
      </main>

      <section id="related">
    <div>
        <h2>Related Posts</h2>
        <div class="grid">
                            <article>
                    <h3><a href="advocacy.html">Advocacy Posts</a></h3>
                    <p>Street advocacy and social justice content.</p>
                </article>
                <article>
                    <h3><a href="personal.html">Personal Stories</a></h3>
                    <p>Personal reflections and experiences.</p>
                </article>
                    </div>
    </div>
    <div>
        <section id="about">
            <h3>About A. A. Chips</h3>
            <p>Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.</p>
        </section>

        <section id="donate">
            <h3>Donate</h3>
            <p>If you like what I am doing. Or if you hate what I am doing. You can donate one-time or recurring pledges through my <a href="https://www.ko-fi.com/aachips">Ko-Fi page</a>.</p>
            <a href="crowdfund.html" target="_blank">What am I raising funds for?</a>
        </section>

        <section id="categories">
            <h3>Categories</h3>
            <ul>
                                    <li>
                                                    <a href="../advocacy.html">Street Advocacy</a>
                                            </li>
                                    <li>
                                                    <a href="../kitchen.html">Apple Chip Kitchen</a>
                                            </li>
                                    <li>
                                                    <a href="../alienation.html">Alienation</a>
                                            </li>
                                    <li>
                                                    <a href="../climate.html">Climate</a>
                                            </li>
                                    <li>
                                                    <a href="#" onclick="showHumorModal()">Humor</a>
                                            </li>
                                    <li>
                                                    <a href="../inspiration.html">Inspiration</a>
                                            </li>
                                    <li>
                                                    <a href="../journal.html">Journal</a>
                                            </li>
                                    <li>
                                                    <a href="../personal.html">Personal Stories</a>
                                            </li>
                                    <li>
                                                    <a href="../writings.html">Writings</a>
                                            </li>
                            </ul>
        </section>

        <section id="connect">
            <h3>Connect</h3>
            <p>Share your thoughts, questions, or just say hello. Use the chat widget below. Leave an email address to reply to.</p>
        </section>
    </div>
</section>
      <footer>
    <div class="container">
        <div class="bottom">
            <p>© 2025 A. A. Chips. All rights reserved.</p>
        </div>
    </div>
</footer>

      <!-- Modal for shorthand content -->
      <div id="contentModal" class="modal" style="display: none;">
          <div class="modal-content">
              <span class="close">&times;</span>
              <div id="modalBody"></div>
          </div>
      </div>

      <script src="../js/script.js"></script>
      <script src="../js/modal.js"></script>
  </body>
</html>
