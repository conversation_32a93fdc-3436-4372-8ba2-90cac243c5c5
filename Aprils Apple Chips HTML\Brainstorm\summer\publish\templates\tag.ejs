<%- include('partials/header', { title: `Posts tagged with #${tag}` }) %>

<h2 class="mb-4">Posts tagged with <span class="badge bg-secondary">#<%= tag %></span></h2>

<% if (posts.length === 0) { %>
  <div class="alert alert-info">
    No posts found with this tag.
  </div>
<% } else { %>
  <% posts.forEach(post => { %>
    <div class="card mb-4">
      <div class="card-body">
        <h3 class="card-title"><a href="/posts/<%= post.slug %>" class="text-decoration-none"><%= post.title %></a></h3>
        <div class="card-subtitle text-muted mb-2">
          <%= post.date %>
          <% if (post.tags && post.tags.length > 0) { %>
            • 
            <% post.tags.forEach((tag, index) => { %>
              <a href="/tag/<%= tag %>" class="badge bg-secondary text-decoration-none">#<%= tag %></a><%= index < post.tags.length - 1 ? ' ' : '' %>
            <% }); %>
          <% } %>
        </div>
        <p class="card-text"><%= post.excerpt %></p>
        <a href="/posts/<%= post.slug %>" class="btn btn-primary">Read More</a>
      </div>
    </div>
  <% }); %>
<% } %>

<%- include('partials/footer') %>
