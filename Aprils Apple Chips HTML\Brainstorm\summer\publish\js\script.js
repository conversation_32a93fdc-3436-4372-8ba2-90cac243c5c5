document.addEventListener('DOMContentLoaded', function() {
  // Image Gallery
  const createGallery = () => {
    const blogPostContent = document.querySelector('.blog-post-content');
    if (!blogPostContent) return;
    
    const images = blogPostContent.querySelectorAll('img');
    if (images.length < 3) return; // Only create gallery if there are at least 3 images
    
    const gallery = document.createElement('div');
    gallery.className = 'image-gallery';
    
    images.forEach((img, index) => {
      // Skip if image is inside a blockquote or has a specific class
      if (img.closest('blockquote') || img.classList.contains('no-gallery')) return;
      
      const imgClone = img.cloneNode(true);
      const galleryItem = document.createElement('div');
      galleryItem.className = 'image-gallery-item';
      galleryItem.appendChild(imgClone);
      
      // Add click event to open image in modal
      galleryItem.addEventListener('click', () => {
        openImageModal(img.src, img.alt);
      });
      
      gallery.appendChild(galleryItem);
      
      // Hide the original image
      img.style.display = 'none';
    });
    
    // Insert gallery after the first paragraph
    const firstParagraph = blogPostContent.querySelector('p');
    if (firstParagraph && gallery.children.length > 0) {
      firstParagraph.after(gallery);
    }
  };
  
  // Image Modal
  const openImageModal = (src, alt) => {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'imageModal';
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', 'imageModalLabel');
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="imageModalLabel">${alt || 'Image'}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body text-center">
            <img src="${src}" class="img-fluid" alt="${alt || 'Image'}">
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    modal.addEventListener('hidden.bs.modal', () => {
      document.body.removeChild(modal);
    });
  };
  
  // Initialize gallery
  createGallery();
  
  // Comment form handling
  const commentForm = document.querySelector('.card form');
  if (commentForm) {
    commentForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      const nameInput = this.querySelector('#name');
      const emailInput = this.querySelector('#email');
      const commentInput = this.querySelector('#comment');
      
      if (!nameInput.value || !emailInput.value || !commentInput.value) {
        alert('Please fill in all fields');
        return;
      }
      
      // In a real application, you would send this data to the server
      alert('Comment functionality is not implemented yet. Your comment would be saved here.');
      
      // Clear form
      nameInput.value = '';
      emailInput.value = '';
      commentInput.value = '';
    });
  }
});
