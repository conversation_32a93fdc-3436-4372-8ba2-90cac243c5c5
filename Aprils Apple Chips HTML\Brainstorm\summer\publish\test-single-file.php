<?php
// Test building a single file to see what's happening

class TestBuilder {
    private function parseMarkdown($content) {
        // Simple YAML frontmatter parser
        if (!preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
            return ['frontmatter' => null, 'content' => $content];
        }
        
        $frontmatter = [];
        $yamlLines = explode("\n", $matches[1]);
        $currentKey = null;
        
        foreach ($yamlLines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            // Handle array items
            if (strpos($line, '-') === 0) {
                if ($currentKey && !isset($frontmatter[$currentKey])) {
                    $frontmatter[$currentKey] = [];
                }
                if ($currentKey) {
                    $frontmatter[$currentKey][] = trim(substr($line, 1));
                }
            } elseif (strpos($line, ':') !== false) {
                [$key, $value] = explode(':', $line, 2);
                $key = trim($key);
                $value = trim($value);
                $currentKey = $key;
                
                if (!empty($value)) {
                    $frontmatter[$key] = $value;
                }
            }
        }
        
        return ['frontmatter' => $frontmatter, 'content' => $matches[2]];
    }
    
    private function generatePostHeader($frontmatter) {
        $header = '';
        
        // Debug: Let's see what frontmatter we have
        if (empty($frontmatter)) {
            return $header;
        }
        
        // Add title
        if (isset($frontmatter['title'])) {
            $header .= "<h1>{$frontmatter['title']}</h1>\n";
        }
        
        // Add post metadata
        $metadata = [];
        if (isset($frontmatter['author'])) {
            $metadata[] = "<span class=\"post-author\">By {$frontmatter['author']}</span>";
        }
        if (isset($frontmatter['date'])) {
            $metadata[] = "<span class=\"post-date\">{$frontmatter['date']}</span>";
        }
        
        if (!empty($metadata)) {
            $header .= "<div class=\"post-meta\">" . implode(' • ', $metadata) . "</div>\n";
        }
        
        // Add excerpt if available
        if (isset($frontmatter['excerpt'])) {
            $header .= "<div class=\"post-excerpt\"><em>{$frontmatter['excerpt']}</em></div>\n";
        }
        
        // Add tags if available
        if (isset($frontmatter['tags'])) {
            $tags = $frontmatter['tags'];
            if (is_array($tags) && !empty($tags)) {
                $header .= "<div class=\"post-tags\">";
                foreach ($tags as $tag) {
                    $header .= "<span class=\"tag\">{$tag}</span>";
                }
                $header .= "</div>\n";
            } elseif (is_string($tags)) {
                $header .= "<div class=\"post-tags\"><span class=\"tag\">{$tags}</span></div>\n";
            }
        }
        
        // Add separator
        $header .= "<hr class=\"post-separator\">\n\n";
        
        return $header;
    }
    
    private function markdownToHtml($markdown) {
        // Simple markdown to HTML conversion
        $html = $markdown;
        
        // Headers (but don't convert if it's already HTML)
        $html = preg_replace('/^### (.*$)/m', '<h3>$1</h3>', $html);
        $html = preg_replace('/^## (.*$)/m', '<h2>$1</h2>', $html);
        // Don't convert # headers if they're already in HTML format
        $html = preg_replace('/^# (?!.*<h1>)(.*$)/m', '<h1>$1</h1>', $html);
        
        // Bold and italic
        $html = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $html);
        $html = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $html);
        
        // Links
        $html = preg_replace('/\[([^\]]+)\]\(([^)]+)\)/', '<a href="$2">$1</a>', $html);
        
        // Images
        $html = preg_replace('/!\[\[([^\]]+)\]\]/', '<img src="img/$1" alt="$1">', $html);
        $html = preg_replace('/!\[([^\]]*)\]\(([^)]+)\)/', '<img src="$2" alt="$1">', $html);
        
        // Lists
        $html = preg_replace('/^\+ (.*)$/m', '<li>$1</li>', $html);
        $html = preg_replace('/^- (.*)$/m', '<li>$1</li>', $html);
        
        // Wrap consecutive list items in ul tags
        $html = preg_replace('/(<li>.*<\/li>)/s', '<ul>$1</ul>', $html);
        
        // Paragraphs
        $lines = explode("\n", $html);
        $result = [];
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                $result[] = '';
                continue;
            }
            
            // Don't wrap HTML tags in paragraphs
            if (strpos($line, '<h') === 0 || strpos($line, '<ul>') === 0 ||
                strpos($line, '<img') === 0 || strpos($line, '<iframe') === 0 ||
                strpos($line, '<div') === 0 || strpos($line, '<hr') === 0 ||
                strpos($line, '</div>') === 0) {
                $result[] = $line;
            } else {
                $result[] = "<p>{$line}</p>";
            }
        }
        
        return implode("\n", $result);
    }
    
    public function testFile($filename) {
        $content = file_get_contents($filename);
        $parsed = $this->parseMarkdown($content);
        
        echo "=== FRONTMATTER ===\n";
        print_r($parsed['frontmatter']);
        
        echo "\n=== RAW MARKDOWN CONTENT ===\n";
        echo substr($parsed['content'], 0, 200) . "...\n";
        
        $htmlContent = $this->markdownToHtml($parsed['content']);
        echo "\n=== CONVERTED HTML CONTENT ===\n";
        echo substr($htmlContent, 0, 200) . "...\n";
        
        $postHeader = $this->generatePostHeader($parsed['frontmatter']);
        echo "\n=== POST HEADER ===\n";
        echo $postHeader;
        
        $fullContent = $postHeader . $htmlContent;
        echo "\n=== FULL CONTENT (first 500 chars) ===\n";
        echo substr($fullContent, 0, 500) . "...\n";
        
        return $fullContent;
    }
}

$builder = new TestBuilder();
$builder->testFile('content/hadestown-review.md');
?>
