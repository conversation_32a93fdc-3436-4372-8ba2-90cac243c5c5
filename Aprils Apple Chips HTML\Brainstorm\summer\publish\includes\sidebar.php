<section id="related">
    <div>
        <h2>Related Posts</h2>
        <div class="grid">
            <?php if (isset($related_posts) && !empty($related_posts)): ?>
                <?php foreach ($related_posts as $post): ?>
                    <article>
                        <h3><a href="<?php echo $post['url']; ?>"><?php echo $post['title']; ?></a></h3>
                        <p><?php echo $post['excerpt']; ?></p>
                    </article>
                <?php endforeach; ?>
            <?php else: ?>
                <article>
                    <h3><a href="advocacy.html">Advocacy Posts</a></h3>
                    <p>Street advocacy and social justice content.</p>
                </article>
                <article>
                    <h3><a href="personal.html">Personal Stories</a></h3>
                    <p>Personal reflections and experiences.</p>
                </article>
            <?php endif; ?>
        </div>
    </div>
    <div>
        <section id="about">
            <h3>About <?php echo htmlspecialchars($config['site']['name'] ?? 'A. A. Chips'); ?></h3>
            <p><?php echo htmlspecialchars($config['site']['description'] ?? 'Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.'); ?></p>
        </section>

        <section id="donate">
            <h3>Donate</h3>
            <p>If you like what I am doing. Or if you hate what I am doing. You can donate one-time or recurring pledges through my <a href="<?php echo $config['social']['kofi'] ?? 'https://www.ko-fi.com/aachips'; ?>">Ko-Fi page</a>.</p>
            <a href="crowdfund.html" target="_blank">What am I raising funds for?</a>
        </section>

        <section id="categories">
            <h3>Categories</h3>
            <ul>
                <?php
                $categories = $config['categories'] ?? [
                    'Street Advocacy' => 'advocacy.html',
                    'Apple Chip Kitchen' => 'kitchen.html',
                    'Alienation' => 'alienation.html',
                    'Climate' => 'climate.html',
                    'Humor' => '#',
                    'Inspiration' => 'inspiration.html',
                    'Journal' => 'journal.html',
                    'Personal Stories' => 'personal.html',
                    'Writings' => 'writings.html'
                ];
                foreach ($categories as $category => $url):
                ?>
                    <li>
                        <?php if ($url === '#'): ?>
                            <a href="#" onclick="showHumorModal()"><?php echo htmlspecialchars($category); ?></a>
                        <?php else: ?>
                            <a href="<?php echo ($base_url ?? '') . $url; ?>"><?php echo htmlspecialchars($category); ?></a>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </section>

        <section id="connect">
            <h3>Connect</h3>
            <p>Share your thoughts, questions, or just say hello. Use the chat widget below. Leave an email address to reply to.</p>
        </section>
    </div>
</section>
